"""
Redis менеджер для кэширования данных
"""
import json
import pickle
import logging
from typing import Any, Optional, Dict, List
from datetime import timedelta
import aioredis
from os import getenv
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

# Настройки Redis
REDIS_HOST = getenv("REDIS_HOST", "redis")
REDIS_PORT = int(getenv("REDIS_PORT", "6379"))
REDIS_DB = int(getenv("REDIS_DB", "0"))
REDIS_PASSWORD = getenv("REDIS_PASSWORD", None)

# TTL по умолчанию (в секундах)
DEFAULT_TTL = 3600  # 1 час
ROLE_CACHE_TTL = 1800  # 30 минут для ролей
KEYBOARD_CACHE_TTL = 600  # 10 минут для клавиатур
FSM_STATE_TTL = 86400  # 24 часа для состояний FSM

class RedisManager:
    """Менеджер для работы с Redis"""
    
    def __init__(self):
        self.redis: Optional[aioredis.Redis] = None
        self.connected = False
    
    async def connect(self):
        """Подключение к Redis"""
        try:
            self.redis = aioredis.from_url(
                f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}",
                password=REDIS_PASSWORD,
                encoding="utf-8",
                decode_responses=False,  # Для работы с pickle
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Проверяем соединение
            await self.redis.ping()
            self.connected = True
            logger.info("✅ Redis подключен успешно")
            
        except Exception as e:
            logger.error(f"❌ Ошибка подключения к Redis: {e}")
            self.connected = False
            self.redis = None
    
    async def disconnect(self):
        """Отключение от Redis"""
        if self.redis:
            await self.redis.close()
            self.connected = False
            logger.info("🔌 Redis отключен")
    
    async def is_connected(self) -> bool:
        """Проверка соединения с Redis"""
        if not self.redis or not self.connected:
            return False
        
        try:
            await self.redis.ping()
            return True
        except Exception:
            self.connected = False
            return False
    
    # === МЕТОДЫ ДЛЯ КЭШИРОВАНИЯ РОЛЕЙ ===
    
    async def set_user_role(self, user_id: int, role: str, ttl: int = ROLE_CACHE_TTL):
        """Сохранить роль пользователя"""
        if not await self.is_connected():
            return False
        
        try:
            key = f"user_role:{user_id}"
            await self.redis.setex(key, ttl, role)
            return True
        except Exception as e:
            logger.error(f"❌ Ошибка сохранения роли пользователя {user_id}: {e}")
            return False
    
    async def get_user_role(self, user_id: int) -> Optional[str]:
        """Получить роль пользователя"""
        if not await self.is_connected():
            return None
        
        try:
            key = f"user_role:{user_id}"
            role = await self.redis.get(key)
            return role.decode('utf-8') if role else None
        except Exception as e:
            logger.error(f"❌ Ошибка получения роли пользователя {user_id}: {e}")
            return None
    
    async def set_roles_cache(self, roles_dict: Dict[str, List[int]], ttl: int = ROLE_CACHE_TTL):
        """Сохранить кэш всех ролей"""
        if not await self.is_connected():
            return False
        
        try:
            # Сохраняем общий кэш ролей
            key = "roles_cache"
            data = json.dumps(roles_dict)
            await self.redis.setex(key, ttl, data)
            
            # Сохраняем индивидуальные роли для быстрого доступа
            for role, user_ids in roles_dict.items():
                for user_id in user_ids:
                    await self.set_user_role(user_id, role, ttl)
            
            return True
        except Exception as e:
            logger.error(f"❌ Ошибка сохранения кэша ролей: {e}")
            return False
    
    async def get_roles_cache(self) -> Optional[Dict[str, List[int]]]:
        """Получить кэш всех ролей"""
        if not await self.is_connected():
            return None
        
        try:
            key = "roles_cache"
            data = await self.redis.get(key)
            if data:
                return json.loads(data.decode('utf-8'))
            return None
        except Exception as e:
            logger.error(f"❌ Ошибка получения кэша ролей: {e}")
            return None
    
    # === МЕТОДЫ ДЛЯ КЭШИРОВАНИЯ КЛАВИАТУР ===
    
    async def set_keyboard(self, key: str, keyboard_data: Any, ttl: int = KEYBOARD_CACHE_TTL):
        """Сохранить клавиатуру в кэш"""
        if not await self.is_connected():
            return False
        
        try:
            cache_key = f"keyboard:{key}"
            # Используем pickle для сериализации объектов aiogram
            data = pickle.dumps(keyboard_data)
            await self.redis.setex(cache_key, ttl, data)
            return True
        except Exception as e:
            logger.error(f"❌ Ошибка сохранения клавиатуры {key}: {e}")
            return False
    
    async def get_keyboard(self, key: str) -> Optional[Any]:
        """Получить клавиатуру из кэша"""
        if not await self.is_connected():
            return None
        
        try:
            cache_key = f"keyboard:{key}"
            data = await self.redis.get(cache_key)
            if data:
                return pickle.loads(data)
            return None
        except Exception as e:
            logger.error(f"❌ Ошибка получения клавиатуры {key}: {e}")
            return None
    
    # === МЕТОДЫ ДЛЯ КЭШИРОВАНИЯ FSM СОСТОЯНИЙ ===
    
    async def set_fsm_state(self, user_id: int, chat_id: int, state: str, data: Dict = None, ttl: int = FSM_STATE_TTL):
        """Сохранить состояние FSM"""
        if not await self.is_connected():
            return False
        
        try:
            state_key = f"fsm_state:{user_id}:{chat_id}"
            data_key = f"fsm_data:{user_id}:{chat_id}"
            
            await self.redis.setex(state_key, ttl, state)
            if data:
                await self.redis.setex(data_key, ttl, json.dumps(data))
            
            return True
        except Exception as e:
            logger.error(f"❌ Ошибка сохранения FSM состояния {user_id}:{chat_id}: {e}")
            return False
    
    async def get_fsm_state(self, user_id: int, chat_id: int) -> tuple[Optional[str], Optional[Dict]]:
        """Получить состояние FSM"""
        if not await self.is_connected():
            return None, None
        
        try:
            state_key = f"fsm_state:{user_id}:{chat_id}"
            data_key = f"fsm_data:{user_id}:{chat_id}"
            
            state = await self.redis.get(state_key)
            data = await self.redis.get(data_key)
            
            state_str = state.decode('utf-8') if state else None
            data_dict = json.loads(data.decode('utf-8')) if data else None
            
            return state_str, data_dict
        except Exception as e:
            logger.error(f"❌ Ошибка получения FSM состояния {user_id}:{chat_id}: {e}")
            return None, None
    
    async def clear_fsm_state(self, user_id: int, chat_id: int):
        """Очистить состояние FSM"""
        if not await self.is_connected():
            return False
        
        try:
            state_key = f"fsm_state:{user_id}:{chat_id}"
            data_key = f"fsm_data:{user_id}:{chat_id}"
            
            await self.redis.delete(state_key, data_key)
            return True
        except Exception as e:
            logger.error(f"❌ Ошибка очистки FSM состояния {user_id}:{chat_id}: {e}")
            return False
    
    # === ОБЩИЕ МЕТОДЫ КЭШИРОВАНИЯ ===
    
    async def set_cache(self, key: str, value: Any, ttl: int = DEFAULT_TTL):
        """Универсальный метод сохранения в кэш"""
        if not await self.is_connected():
            return False
        
        try:
            if isinstance(value, (dict, list)):
                data = json.dumps(value)
            elif isinstance(value, (int, float, str)):
                data = str(value)
            else:
                data = pickle.dumps(value)
            
            await self.redis.setex(f"cache:{key}", ttl, data)
            return True
        except Exception as e:
            logger.error(f"❌ Ошибка сохранения в кэш {key}: {e}")
            return False
    
    async def get_cache(self, key: str) -> Optional[Any]:
        """Универсальный метод получения из кэша"""
        if not await self.is_connected():
            return None
        
        try:
            data = await self.redis.get(f"cache:{key}")
            if not data:
                return None
            
            # Пытаемся десериализовать как JSON
            try:
                return json.loads(data.decode('utf-8'))
            except (json.JSONDecodeError, UnicodeDecodeError):
                # Если не JSON, пытаемся как pickle
                try:
                    return pickle.loads(data)
                except:
                    # Если не pickle, возвращаем как строку
                    return data.decode('utf-8')
        except Exception as e:
            logger.error(f"❌ Ошибка получения из кэша {key}: {e}")
            return None
    
    async def delete_cache(self, key: str):
        """Удалить из кэша"""
        if not await self.is_connected():
            return False
        
        try:
            await self.redis.delete(f"cache:{key}")
            return True
        except Exception as e:
            logger.error(f"❌ Ошибка удаления из кэша {key}: {e}")
            return False
    
    async def clear_all_cache(self):
        """Очистить весь кэш"""
        if not await self.is_connected():
            return False
        
        try:
            await self.redis.flushdb()
            logger.info("🧹 Весь кэш очищен")
            return True
        except Exception as e:
            logger.error(f"❌ Ошибка очистки кэша: {e}")
            return False

# Глобальный экземпляр Redis менеджера
redis_manager = RedisManager()
